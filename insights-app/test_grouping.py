#!/usr/bin/env python3

"""
Test script to verify the new grouping logic for display vs download data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from insightsapp.api_views import group_small_positions

def test_top_positions():
    """Test Top Positions attribute - should show top 10, exclude Other/Cash"""
    print("Testing Top Positions attribute:")
    
    # Mock data with more than 10 items including Other and Cash
    top_positions_data = {
        "Apple Inc": 8.5,
        "Microsoft Corp": 7.2,
        "Amazon.com Inc": 6.8,
        "Alphabet Inc": 5.9,
        "Tesla Inc": 4.3,
        "Meta Platforms": 3.8,
        "NVIDIA Corp": 3.5,
        "Netflix Inc": 2.9,
        "Adobe Inc": 2.4,
        "Salesforce Inc": 2.1,
        "PayPal Holdings": 1.8,
        "Intel Corp": 1.5,
        "Cisco Systems": 1.2,
        "Oracle Corp": 0.9,
        "IBM Corp": 0.7,
        "Other": 5.2,  # Should be excluded
        "Cash": 2.1    # Should be excluded
    }
    
    result = group_small_positions(top_positions_data, "Top Positions")
    
    print(f"Display data items: {len(result['display_data'])}")
    print(f"Full data items: {len(result['full_data'])}")
    print(f"Display data: {list(result['display_data'].keys())}")
    print(f"Full data: {list(result['full_data'].keys())}")
    
    # Verify Other and Cash are excluded
    assert "Other" not in result['display_data'], "Other should be excluded from Top Positions display"
    assert "Cash" not in result['display_data'], "Cash should be excluded from Top Positions display"
    assert "Other" not in result['full_data'], "Other should be excluded from Top Positions full data"
    assert "Cash" not in result['full_data'], "Cash should be excluded from Top Positions full data"
    
    # Verify we have exactly 10 items (or less if fewer than 10 valid items)
    assert len(result['display_data']) == 10, f"Expected 10 items, got {len(result['display_data'])}"
    
    print("✓ Top Positions test passed\n")

def test_regular_attribute():
    """Test regular attribute - should show top 10, rest in Other"""
    print("Testing regular attribute (Currency):")
    
    # Mock data with more than 10 items
    currency_data = {
        "USD": 45.2,
        "EUR": 35.8,
        "DKK": 12.5,
        "GBP": 6.5,
        "JPY": 3.2,
        "CHF": 2.8,
        "CAD": 2.1,
        "AUD": 1.9,
        "SEK": 1.5,
        "NOK": 1.2,
        "PLN": 0.8,
        "CZK": 0.6,
        "HUF": 0.4,
        "RON": 0.3,
        "BGN": 0.2
    }
    
    result = group_small_positions(currency_data, "Currency")
    
    print(f"Display data items: {len(result['display_data'])}")
    print(f"Full data items: {len(result['full_data'])}")
    print(f"Display data: {list(result['display_data'].keys())}")
    
    # Verify we have at most 11 items in display (10 + Other)
    assert len(result['display_data']) <= 11, f"Expected at most 11 items, got {len(result['display_data'])}"
    
    # Verify "Other" is present in display data (since we have more than 10 items)
    assert "Other" in result['display_data'], "Other should be present when more than 10 items"
    
    # Verify full data has all original items
    assert len(result['full_data']) == len(currency_data), "Full data should contain all original items"
    
    print("✓ Regular attribute test passed\n")

def test_small_dataset():
    """Test with fewer than 10 items - should show all"""
    print("Testing small dataset (5 items):")
    
    small_data = {
        "USD": 50.0,
        "EUR": 30.0,
        "GBP": 15.0,
        "JPY": 3.0,
        "CHF": 2.0
    }
    
    result = group_small_positions(small_data, "Currency")
    
    print(f"Display data items: {len(result['display_data'])}")
    print(f"Full data items: {len(result['full_data'])}")
    
    # Should show all items, no "Other"
    assert len(result['display_data']) == 5, f"Expected 5 items, got {len(result['display_data'])}"
    assert "Other" not in result['display_data'], "Other should not be present with 5 items"
    assert len(result['full_data']) == 5, "Full data should have 5 items"
    
    print("✓ Small dataset test passed\n")

if __name__ == "__main__":
    print("Testing new grouping logic...\n")
    
    test_top_positions()
    test_regular_attribute()
    test_small_dataset()
    
    print("All tests passed! ✓")
